import { TEMPLATE_PARTS } from "../constants";
import type { PropertyAndSequence } from "../../types/types";
import { ParameterParser, type ParameterInfo, type FunctionParameter } from "../parsers/parameter-parser";
import { CallParser } from "../parsers/call-parser";

export interface TestGenerationOptions {
  identifier: string;
  index: number;
}

export class TestGenerator {
  static generateTestFunction(
    propSeq: PropertyAndSequence, 
    options: TestGenerationOptions
  ): string {
    const { identifier, index } = options;
    const functionName = this.createFunctionName(propSeq.brokenProperty, identifier, index);
    
    const sequences = Array.isArray(propSeq.sequence) ? propSeq.sequence : [propSeq.sequence];
    const { parameters, calls } = this.processSequences(sequences);
    
    const functionSignature = this.parseFunctionSignature(propSeq.brokenProperty);
    const paramDeclarations = this.generateParameterDeclarations(functionSignature.parameters, parameters);
    const callStatements = this.generateCallStatements(calls, parameters);
    
    return this.assembleFunction(functionName, paramDeclarations, callStatements, propSeq.brokenProperty);
  }

  static generateSequenceFunction(
    sequences: string[], 
    brokenProperty: string, 
    identifier: string
  ): string {
    const functionName = this.createSequenceFunctionName(brokenProperty, identifier);
    const { parameters, calls } = this.processSequences(sequences);
    
    const functionSignature = this.parseFunctionSignature(brokenProperty);
    const paramDeclarations = this.generateParameterDeclarations(functionSignature.parameters, parameters);
    const callStatements = this.generateCallStatements(calls, parameters);
    
    return this.assembleFunction(functionName, paramDeclarations, callStatements, brokenProperty);
  }

  private static createFunctionName(brokenProperty: string, identifier: string, index: number): string {
    const cleanProperty = brokenProperty.replace(/[^a-zA-Z0-9_]/g, "_");
    return `${TEMPLATE_PARTS.FUNCTION_PREFIX}${identifier}_${cleanProperty}_${index}`;
  }

  private static createSequenceFunctionName(brokenProperty: string, identifier: string): string {
    const cleanProperty = brokenProperty.replace(/[^a-zA-Z0-9_]/g, "_");
    return `${TEMPLATE_PARTS.FUNCTION_PREFIX}${identifier}_${cleanProperty}`;
  }

  private static processSequences(sequences: string[]): { parameters: ParameterInfo[]; calls: string[] } {
    const parameters: ParameterInfo[] = [];
    const calls: string[] = [];

    sequences.forEach(line => {
      const paramInfo = ParameterParser.parseParameterDeclaration(line);
      if (paramInfo) {
        parameters.push(paramInfo);
      } else if (line.startsWith("CALL ")) {
        calls.push(line);
      }
    });

    return { parameters, calls };
  }

  private static parseFunctionSignature(brokenProperty: string): { name: string; parameters: FunctionParameter[] } {
    return ParameterParser.parseFunctionSignature(brokenProperty);
  }

  private static generateParameterDeclarations(
    functionParams: FunctionParameter[], 
    availableParams: ParameterInfo[]
  ): string[] {
    if (functionParams.length === 0) {
      return availableParams.map(param => `${param.type} ${param.name} = ${param.value};`);
    }

    return functionParams.map(param => {
      const matchedParam = ParameterParser.matchParameterToType(param.name, availableParams);
      const paramInfo = availableParams.find(p => p.name === matchedParam);
      const value = paramInfo?.value || ParameterParser.generateParameterDeclarations([param])[0].split(' = ')[1];
      return `${param.type} ${param.name} = ${value};`;
    });
  }

  private static generateCallStatements(calls: string[], parameters: ParameterInfo[]): string[] {
    return calls.map(callLine => {
      const parsedCall = CallParser.parseCallStatement(callLine);
      if (!parsedCall) return `// ${callLine}`;

      const mappedParams = parsedCall.parameters.map(param => 
        ParameterParser.matchParameterToType(param, parameters)
      );

      return `${parsedCall.functionName}(${mappedParams.join(", ")});`;
    });
  }

  private static assembleFunction(
    functionName: string,
    paramDeclarations: string[],
    callStatements: string[],
    brokenProperty: string
  ): string {
    const parts = [
      `${functionName}${TEMPLATE_PARTS.FUNCTION_SUFFIX}`,
      "",
      `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.SETUP_COMMENT}`,
      ...paramDeclarations.map(decl => `    ${decl}`),
      "",
      `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.REPRO_COMMENT}`,
      ...callStatements.map(stmt => `    ${stmt}`),
      "",
      `${TEMPLATE_PARTS.COMMENT_PREFIX}${TEMPLATE_PARTS.INVARIANT_COMMENT}`,
      `    ${TEMPLATE_PARTS.COMMENT_PREFIX}${brokenProperty}`,
      "",
      TEMPLATE_PARTS.FUNCTION_END
    ];

    return parts.join("\n");
  }
}
