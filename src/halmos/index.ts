import { type FuzzingResults, type PropertyAndSequence } from "../types/types";
import { LineProcessor } from "./processors/line-processor";
import { TestGenerator } from "./generators/test-generator";
import { SequenceExtractor } from "./parsers/sequence-extractor";

// Store all lines for batch processing
let allLines: string[] = [];

export function processHalmos(line: string, jobStats: FuzzingResults): void {
  // Store all lines for later processing
  allLines.push(line);

  // Process statistics using LineProcessor
  LineProcessor.processStatsLine(line, jobStats);

  // Process broken properties when we hit a FAIL/TIMEOUT line
  if (line.includes("[FAIL]") || line.includes("[TIMEOUT]")) {
    // Process all accumulated lines to extract broken properties
    const logsText = allLines.join("\n");
    const propertySequences = getHalmosPropertyAndSequence(logsText);

    // Add any new broken properties that aren't already in jobStats
    propertySequences.forEach((propSeq) => {
      const exists = jobStats.brokenProperties.some(
        (existing) => existing.brokenProperty === propSeq.brokenProperty
      );
      if (!exists) {
        const sequenceString = Array.isArray(propSeq.sequence)
          ? propSeq.sequence.join("\n")
          : propSeq.sequence;
        jobStats.brokenProperties.push({
          brokenProperty: propSeq.brokenProperty,
          sequence: sequenceString,
        });
      }
    });
  }

  // Extract duration from summary line
  if (line.includes("Symbolic test result:")) {
    const durationMatch = line.match(/time: ([^,]+)/);
    if (durationMatch) {
      jobStats.duration = durationMatch[1];
    }
  }

  // Extract coverage from summary line
  if (line.includes("Symbolic test result:")) {
    const coverageMatch = line.match(/coverage: (\d+)%/);
    if (coverageMatch) {
      jobStats.coverage = parseInt(coverageMatch[1]);
    }
  }

  // Reset for next batch when we see a new counterexample
  if (LineProcessor.isCounterexampleStart(line)) {
    allLines = [line];
  }

  // Always add to traces
  LineProcessor.addToTraces(line, jobStats);
}

export function getHalmosPropertyAndSequence(
  logs: string
): PropertyAndSequence[] {
  return SequenceExtractor.extractPropertyAndSequence(logs);
}

export function halmosSequenceToFunction(
  sequence: string,
  brokenProperty: string,
  identifier: string
): string {
  const sequenceArray = sequence
    .split("\n")
    .filter((line) => line.trim() !== "");

  return TestGenerator.generateSequenceFunction(
    sequenceArray,
    brokenProperty,
    identifier
  );
}

export function halmosLogsToFunctions(
  logs: string,
  identifier: string
): string {
  const propertySequences = getHalmosPropertyAndSequence(logs);

  return propertySequences.length === 0
    ? "// No failed properties found in Halmos logs"
    : propertySequences
        .map((propSeq, index) =>
          TestGenerator.generateTestFunction(propSeq, { identifier, index })
        )
        .join("\n\n");
}
