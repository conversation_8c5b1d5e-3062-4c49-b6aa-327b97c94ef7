import { TYPE_PATTERNS, DEFAULT_TYPES, PATTERNS } from "../constants";

export interface ParameterInfo {
  name: string;
  value: string;
  type: string;
}

export interface FunctionParameter {
  name: string;
  type: string;
}

export class ParameterParser {
  static extractTypeFromParamName(paramName: string): string {
    for (const [type, patterns] of Object.entries(TYPE_PATTERNS)) {
      if (patterns.some(pattern => paramName.includes(pattern))) {
        return type;
      }
    }
    return DEFAULT_TYPES.FALLBACK;
  }

  static parseParameterDeclaration(line: string): ParameterInfo | null {
    const match = line.match(PATTERNS.PARAMETER_DECLARATION);
    if (!match) return null;

    const name = match[1].trim();
    const value = match[2].trim();
    const type = this.extractTypeFromParamName(name);

    return { name, value, type };
  }

  static parseFunctionSignature(signature: string): { name: string; parameters: FunctionParameter[] } {
    const match = signature.match(PATTERNS.FUNCTION_SIGNATURE);
    if (!match) {
      return { name: signature, parameters: [] };
    }

    const functionName = match[1].trim();
    const paramString = match[2].trim();

    if (!paramString) {
      return { name: functionName, parameters: [] };
    }

    const parameters = this.parseParameterTypes(paramString);
    return { name: functionName, parameters };
  }

  static matchParameterToType(paramName: string, availableParams: ParameterInfo[]): string {
    const exactMatch = availableParams.find(p => p.name === paramName);
    if (exactMatch) return exactMatch.name;

    const typeFromName = this.extractTypeFromParamName(paramName);
    const typeMatch = availableParams.find(p => p.type === typeFromName);
    if (typeMatch) return typeMatch.name;

    const baseNameMatch = availableParams.find(p => 
      this.extractBaseName(p.name) === this.extractBaseName(paramName)
    );
    if (baseNameMatch) return baseNameMatch.name;

    return availableParams.length > 0 ? availableParams[0].name : paramName;
  }

  static generateParameterDeclarations(parameters: FunctionParameter[]): string[] {
    return parameters.map((param, index) => {
      const paramName = param.name || `param${index}`;
      const defaultValue = this.getDefaultValueForType(param.type);
      return `${param.type} ${paramName} = ${defaultValue};`;
    });
  }

  private static parseParameterTypes(paramString: string): FunctionParameter[] {
    const types = paramString.split(',').map(t => t.trim()).filter(t => t);
    return types.map((type, index) => ({
      name: `param${index}`,
      type: type || DEFAULT_TYPES.FALLBACK
    }));
  }

  private static extractBaseName(paramName: string): string {
    return paramName.split('_')[0];
  }

  private static getDefaultValueForType(type: string): string {
    const defaults: Record<string, string> = {
      'address': 'address(0)',
      'bool': 'false',
      'string': '""',
      'bytes': 'hex""',
    };

    if (type.startsWith('uint') || type.startsWith('int')) {
      return '0';
    }

    if (type.startsWith('bytes') && type.length > 5) {
      return 'hex""';
    }

    return defaults[type] || '0';
  }
}
