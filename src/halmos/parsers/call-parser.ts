import { PATTERNS } from "../constants";

export interface ParsedCall {
  functionName: string;
  parameters: string[];
}

export class CallParser {
  static extractCallStatement(line: string): string | null {
    const callStart = line.indexOf("CALL ");
    if (callStart === -1) return null;

    const functionMatch = line.match(PATTERNS.CALL_STATEMENT);
    if (!functionMatch) return null;

    const functionName = functionMatch[1];
    const openParenIndex = line.indexOf("(", callStart);
    if (openParenIndex === -1) return null;

    const params = this.extractParametersWithParentheses(line, openParenIndex);
    if (params === null) return null;

    return `CALL ${functionName}(${params})`;
  }

  static parseCallStatement(callLine: string): ParsedCall | null {
    const callMatch = callLine.match(PATTERNS.CALL_FULL_MATCH);
    if (!callMatch) return null;

    const functionName = callMatch[1];
    const paramString = callMatch[2].trim();

    if (!paramString) {
      return { functionName, parameters: [] };
    }

    if (paramString.startsWith("Concat(")) {
      const concatContent = paramString.slice(7, -1);
      const concatParams = this.parseConcatParameters(concatContent);
      return { functionName, parameters: concatParams };
    }

    const parameters = this.parseParameters(paramString);
    return { functionName, parameters };
  }

  private static extractParametersWithParentheses(line: string, openParenIndex: number): string | null {
    let parenCount = 1;
    let i = openParenIndex + 1;
    
    while (i < line.length && parenCount > 0) {
      if (line[i] === "(") parenCount++;
      else if (line[i] === ")") parenCount--;
      i++;
    }

    return parenCount === 0 ? line.substring(openParenIndex + 1, i - 1) : null;
  }

  private static parseParameters(paramString: string): string[] {
    const parameters: string[] = [];
    let currentParam = "";
    let parenDepth = 0;
    let i = 0;

    while (i < paramString.length) {
      const char = paramString[i];

      if (char === "(") {
        parenDepth++;
        currentParam += char;
      } else if (char === ")") {
        parenDepth--;
        currentParam += char;
      } else if (char === "," && parenDepth === 0) {
        if (currentParam.trim()) {
          parameters.push(currentParam.trim());
        }
        currentParam = "";
      } else {
        currentParam += char;
      }
      i++;
    }

    if (currentParam.trim()) {
      parameters.push(currentParam.trim());
    }

    return parameters;
  }

  private static parseConcatParameters(concatContent: string): string[] {
    const parameters: string[] = [];
    let currentParam = "";
    let parenDepth = 0;
    let i = 0;

    while (i < concatContent.length) {
      const char = concatContent[i];

      if (char === "(") {
        parenDepth++;
        currentParam += char;
      } else if (char === ")") {
        parenDepth--;
        currentParam += char;
      } else if (char === "," && parenDepth === 0) {
        if (currentParam.trim()) {
          parameters.push(currentParam.trim());
        }
        currentParam = "";
      } else {
        currentParam += char;
      }
      i++;
    }

    if (currentParam.trim()) {
      parameters.push(currentParam.trim());
    }

    return parameters;
  }
}
