import type { PropertyAndSequence } from "../../types/types";
import { LineProcessor } from "../processors/line-processor";
import {
  SequenceProcessor,
  type SequenceState,
} from "../processors/sequence-processor";

export class SequenceExtractor {
  static extractPropertyAndSequence(logs: string): PropertyAndSequence[] {
    const results: PropertyAndSequence[] = [];
    const lines = logs.split("\n");

    const state: SequenceState = {
      capturing: false,
      capturingSequence: false,
      currentCall: "",
      currentProperty: "",
      currentCounterexample: [],
      currentSequenceCalls: [],
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      this.processPropertyExtraction(line, state);

      if (this.shouldStartCapturing(line, state)) {
        continue;
      }

      if (this.shouldStartSequenceCapturing(line, state)) {
        continue;
      }

      if (state.capturing) {
        if (this.shouldEndCapturing(line, state, i, lines.length)) {
          this.finalizeAndAddResult(state, results);
        } else {
          this.processCapturingLine(line, state);
        }
      }
    }

    return results;
  }

  private static processPropertyExtraction(
    line: string,
    state: SequenceState
  ): void {
    if (LineProcessor.isFailLine(line)) {
      const property = LineProcessor.extractPropertyFromFailLine(line);
      if (property) {
        state.currentProperty = property;
      }
    }

    // Format 2: Assertion failure detected in ContractName.property_name()
    const assertionFailMatch =
      /Assertion failure detected in \w+\.(.+?)\(\)/.exec(line);
    if (assertionFailMatch) {
      state.currentProperty = assertionFailMatch[1].trim();
    }
  }

  private static shouldStartCapturing(
    line: string,
    state: SequenceState
  ): boolean {
    if (LineProcessor.isCounterexampleStart(line)) {
      state.capturing = true;
      state.capturingSequence = false;
      state.currentCounterexample = [];
      state.currentSequenceCalls = [];
      state.currentCall = "";
      return true;
    }
    return false;
  }

  private static shouldStartSequenceCapturing(
    line: string,
    state: SequenceState
  ): boolean {
    if (line === "Sequence:" || line.includes("Sequence:")) {
      state.capturingSequence = true;
      state.currentCall = "";
      return true;
    }
    return false;
  }

  private static shouldEndCapturing(
    line: string,
    state: SequenceState,
    index: number,
    totalLines: number
  ): boolean {
    const isFailLine = LineProcessor.isFailLine(line);
    const isSymbolicResult = line.includes("Symbolic test result:");
    const isLastLine = Boolean(
      state.currentProperty && index === totalLines - 1
    );

    return isFailLine || isSymbolicResult || isLastLine;
  }

  private static finalizeAndAddResult(
    state: SequenceState,
    results: PropertyAndSequence[]
  ): void {
    SequenceProcessor.finalizeCurrentCall(state);

    if (state.currentProperty && state.currentCounterexample.length > 0) {
      const combinedSequence = [
        ...state.currentCounterexample,
        ...state.currentSequenceCalls,
      ];
      results.push({
        brokenProperty: state.currentProperty,
        sequence: combinedSequence,
      });
    }

    // Reset state
    state.capturing = false;
    state.capturingSequence = false;
    state.currentCounterexample = [];
    state.currentSequenceCalls = [];
    state.currentCall = "";
    state.currentProperty = "";
  }

  private static processCapturingLine(
    line: string,
    state: SequenceState
  ): void {
    if (SequenceProcessor.shouldSkipSequenceLine(line, state)) {
      return;
    }

    if (SequenceProcessor.isCallStart(line)) {
      SequenceProcessor.processCallLine(line, state);
    } else if (SequenceProcessor.isCallContinuation(line, state)) {
      SequenceProcessor.processCallContinuation(line, state);
    } else if (SequenceProcessor.isParameterDeclaration(line)) {
      SequenceProcessor.processParameterLine(line, state);
    }
  }
}
