export const PATTERNS = {
  CALL_STATEMENT: /CALL\s+(\w+::[\w]+)\(/,
  CALL_FULL_MATCH: /CALL\s+\w+::(\w+)\(([^)]*)\)/,
  TEST_COUNT: /Running (\d+) tests/,
  PARAMETER_DECLARATION: /^([^=]+)=(.+)$/,
  FUNCTION_SIGNATURE: /^([^(]+)\(([^)]*)\)$/,
  CONCAT_EXPRESSION: /^Concat\((.+)\)$/,
} as const;

export const LOG_MARKERS = {
  FAIL: "[FAIL]",
  TIMEOUT: "[TIMEOUT]", 
  PASS: "[PASS]",
  COUNTEREXAMPLE: "Counterexample:",
  RUNNING_TESTS: "Running",
  TESTS_FOR: "tests for",
} as const;

export const CALL_PREFIXES = {
  CALL: "CALL ",
  FOUR_SPACES: "    CALL ",
  EIGHT_SPACES: "        ",
} as const;

export const SKIP_PATTERNS = [
  "SLOAD",
  "SSTORE", 
  "STATICCALL",
  "CREATE",
  "↩ RETURN",
  "(value:",
  "(caller:",
  "halmos_msg_",
] as const;

export const TYPE_PATTERNS = {
  address: ["_address"],
  bool: ["_bool"],
  uint256: ["_uint256"],
  uint8: ["_uint8"],
  uint16: ["_uint16"],
  uint32: ["_uint32"],
  uint64: ["_uint64"],
  uint128: ["_uint128"],
  bytes: ["_bytes"],
  string: ["_string"],
} as const;

export const PARAMETER_PATTERNS = [
  "p_",
  "_uint256",
  "_address", 
  "_bool",
  "halmos_",
] as const;

export const DEFAULT_TYPES = {
  FALLBACK: "uint256",
} as const;

export const TEMPLATE_PARTS = {
  FUNCTION_PREFIX: "function test_",
  FUNCTION_SUFFIX: "() public {",
  FUNCTION_END: "}",
  COMMENT_PREFIX: "// ",
  SETUP_COMMENT: "// Setup",
  REPRO_COMMENT: "// Reproduction",
  INVARIANT_COMMENT: "// Invariant that should hold",
} as const;
