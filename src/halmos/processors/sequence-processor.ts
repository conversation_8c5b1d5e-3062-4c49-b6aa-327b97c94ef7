import { CALL_PREFIXES, SKIP_PATTERNS, PARAMETER_PATTERNS } from "../constants";

export interface SequenceState {
  capturing: boolean;
  capturingSequence: boolean;
  currentCall: string;
  currentProperty: string;
  currentCounterexample: string[];
  currentSequenceCalls: string[];
}

export class SequenceProcessor {
  static isCallStart(line: string): boolean {
    return line.startsWith(CALL_PREFIXES.FOUR_SPACES);
  }

  static isCallContinuation(line: string, state: SequenceState): boolean {
    return (
      state.capturingSequence &&
      Boolean(state.currentCall) &&
      !line.startsWith(CALL_PREFIXES.FOUR_SPACES) &&
      !line.startsWith(CALL_PREFIXES.EIGHT_SPACES) &&
      Boolean(line.trim()) &&
      !line.includes("[FAIL]") &&
      !line.includes("[TIMEOUT]") &&
      (line.includes(")") || line.includes(",") || line.includes("p_"))
    );
  }

  static shouldSkipSequenceLine(line: string, state: SequenceState): boolean {
    if (!state.capturingSequence) return false;

    // Skip value and caller information
    if (
      line.includes("(value:") ||
      line.includes("(caller:") ||
      line.startsWith("halmos_msg_")
    ) {
      return true;
    }

    // Skip internal EVM operations
    if (
      line.startsWith("    ") &&
      SKIP_PATTERNS.some((pattern) => line.includes(pattern))
    ) {
      return true;
    }

    return false;
  }

  static isParameterDeclaration(line: string): boolean {
    if (!line.includes("=")) return false;

    return PARAMETER_PATTERNS.some(
      (pattern) => line.startsWith(pattern) || line.includes(pattern)
    );
  }

  static processCallLine(line: string, state: SequenceState): void {
    if (state.currentCall) {
      state.currentSequenceCalls.push(state.currentCall);
    }
    state.currentCall = line;
  }

  static processCallContinuation(line: string, state: SequenceState): void {
    state.currentCall += " " + line.trim();
  }

  static processParameterLine(line: string, state: SequenceState): void {
    state.currentCounterexample.push(line.trim());
  }

  static finalizeCurrentCall(state: SequenceState): void {
    if (state.currentCall) {
      state.currentSequenceCalls.push(state.currentCall);
      state.currentCall = "";
    }
  }
}
