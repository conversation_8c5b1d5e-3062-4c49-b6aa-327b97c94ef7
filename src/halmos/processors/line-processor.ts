import { LOG_MARKERS, PATTERNS } from "../constants";
import type { FuzzingResults } from "../../types/types";

export interface LineProcessingResult {
  shouldCapture: boolean;
  shouldCaptureSequence: boolean;
  shouldReset: boolean;
  property?: string;
}

export class LineProcessor {
  static processStatsLine(line: string, jobStats: FuzzingResults): void {
    if (line.includes(LOG_MARKERS.FAIL) || line.includes(LOG_MARKERS.TIMEOUT)) {
      jobStats.failed++;
    } else if (line.includes(LOG_MARKERS.PASS)) {
      jobStats.passed++;
    }

    if (line.includes(LOG_MARKERS.RUNNING_TESTS) && line.includes(LOG_MARKERS.TESTS_FOR)) {
      const testCountMatch = line.match(PATTERNS.TEST_COUNT);
      if (testCountMatch) {
        jobStats.numberOfTests = parseInt(testCountMatch[1]);
      }
    }
  }

  static isCounterexampleStart(line: string): boolean {
    return line.includes(LOG_MARKERS.COUNTEREXAMPLE);
  }

  static isFailLine(line: string): boolean {
    return line.includes(LOG_MARKERS.FAIL) || line.includes(LOG_MARKERS.TIMEOUT);
  }

  static extractPropertyFromFailLine(line: string): string {
    const match = line.match(/\[FAIL\]\s*([^(]+(?:\([^)]*\))?)/);
    return match ? match[1].trim() : "";
  }

  static shouldSkipLine(line: string): boolean {
    return !line.trim() || 
           line.includes(LOG_MARKERS.FAIL) || 
           line.includes(LOG_MARKERS.TIMEOUT);
  }

  static addToTraces(line: string, jobStats: FuzzingResults): void {
    if (line.trim()) {
      jobStats.traces.push(line.trim());
    }
  }
}
